import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/task_utils.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'timer_state.dart';

class TimerCubit extends Cubit<TimerState> {
  final RealmDatabase _database;
  Timer? _timer;

  TimerCubit(this._database) : super(TimerInitial());

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }

  Future<void> loadTimerState(int taskId) async {
    try {
      final task = _getTaskById(taskId);
      if (task == null) {
        emit(const TimerError('Task not found'));
        return;
      }

      final currentState = _determineTimerState(task);
      emit(currentState);

      // Start periodic updates if timer is running
      if (currentState is TimerRunning) {
        _startPeriodicUpdates(taskId);
      }
    } catch (e) {
      logger('TimerCubit: Error loading timer state: $e');
      emit(const TimerError('Failed to load timer state'));
    }
  }

  Future<void> startTimer(int taskId) async {
    try {
      final task = _getTaskById(taskId);
      if (task == null) {
        emit(const TimerError('Task not found'));
        return;
      }

      // Update task commencement timestamp
      final now = DateTime.now();
      await TaskUtils.updateTaskCommencementTimestamp(taskId.toString(), now);

      final newState = TimerRunning(
        taskId: taskId,
        startTime: now,
        elapsed: Duration.zero,
      );

      emit(newState);
      _startPeriodicUpdates(taskId);

      logger('TimerCubit: Timer started for task $taskId');
    } catch (e) {
      logger('TimerCubit: Error starting timer: $e');
      emit(const TimerError('Failed to start timer'));
    }
  }

  Future<void> pauseTimer(int taskId) async {
    try {
      final task = _getTaskById(taskId);
      if (task == null) {
        emit(const TimerError('Task not found'));
        return;
      }

      final now = DateTime.now();

      // Process pause logic similar to Java code
      if (task.resumePauseItems.isEmpty) {
        // No pause item exists, create first one
        _addResumePauseItem(taskId, '1', null, now);
      } else {
        // Find the last item and update its pause date
        final lastItem = _getLastResumePauseItem(task.resumePauseItems);
        if (lastItem != null) {
          _updateResumePauseItemPauseDate(taskId, lastItem.resumeOrderID!, now);
        }
      }

      final elapsed = _calculateElapsedTime(task);
      final newState = TimerPaused(
        taskId: taskId,
        startTime: task.taskCommencementTimeStamp ?? now,
        elapsed: elapsed,
      );

      emit(newState);
      _timer?.cancel();

      logger('TimerCubit: Timer paused for task $taskId');
    } catch (e) {
      logger('TimerCubit: Error pausing timer: $e');
      emit(const TimerError('Failed to pause timer'));
    }
  }

  Future<void> resumeTimer(int taskId) async {
    try {
      final task = _getTaskById(taskId);
      if (task == null) {
        emit(const TimerError('Task not found'));
        return;
      }

      final now = DateTime.now();

      // Process resume logic similar to Java code
      if (task.resumePauseItems.isEmpty) {
        // Error => there should be at least 1 item because resume happens after pause physically
        logger(
            'TimerCubit: Error - trying to resume timer that was never paused');
        emit(const TimerError('Cannot resume timer that was never paused'));
        return;
      } else {
        // Pause item exists already >> find current pause item and its resume_order_id, and insert new one with +1
        final maxId = _getMaxResumeOrderId(task.resumePauseItems);
        final nextId = maxId + 1;

        _addResumePauseItem(taskId, nextId.toString(), now, null);
      }

      final elapsed = _calculateElapsedTime(task);
      final newState = TimerRunning(
        taskId: taskId,
        startTime: task.taskCommencementTimeStamp ?? now,
        elapsed: elapsed,
      );

      emit(newState);
      _startPeriodicUpdates(taskId);

      logger('TimerCubit: Timer resumed for task $taskId');
    } catch (e) {
      logger('TimerCubit: Error resuming timer: $e');
      emit(const TimerError('Failed to resume timer'));
    }
  }

  Future<void> stopTimer(int taskId) async {
    try {
      final task = _getTaskById(taskId);
      if (task == null) {
        emit(const TimerError('Task not found'));
        return;
      }

      final now = DateTime.now();

      // Update task stopped timestamp
      await TaskUtils.updateTaskStoppedTimestamp(taskId.toString(), now);

      final totalElapsed = _calculateElapsedTime(task);
      final newState = TimerStopped(
        taskId: taskId,
        totalElapsed: totalElapsed,
      );

      emit(newState);
      _timer?.cancel();

      logger('TimerCubit: Timer stopped for task $taskId');
    } catch (e) {
      logger('TimerCubit: Error stopping timer: $e');
      emit(const TimerError('Failed to stop timer'));
    }
  }

  TimerState _determineTimerState(TaskDetailModel task) {
    final taskId = task.taskId ?? 0;

    // If task has stopped timestamp, it's stopped
    if (task.taskStoppedTimeStamp != null) {
      return TimerStopped(
        taskId: taskId,
        totalElapsed: _calculateElapsedTime(task),
      );
    }

    // If task has commencement timestamp but no stop timestamp
    if (task.taskCommencementTimeStamp != null) {
      // Check pause/resume state following Java specifications
      if (task.resumePauseItems.isEmpty) {
        // Never paused, so running (show Pause button)
        return TimerRunning(
          taskId: taskId,
          startTime: task.taskCommencementTimeStamp!,
          elapsed: _calculateElapsedTime(task),
        );
      } else {
        // Find last pause/resume item
        final lastItem = _getLastResumePauseItem(task.resumePauseItems);
        if (lastItem != null) {
          // Following Java logic:
          // if (lastItem.getResumeDate()==null && lastItem.getPauseDate() != null)
          if (lastItem.resumeDate == null && lastItem.pauseDate != null) {
            // This was started/paused >> need to show Resume button
            return TimerPaused(
              taskId: taskId,
              startTime: task.taskCommencementTimeStamp!,
              elapsed: _calculateElapsedTime(task),
            );
          }
          // else if (lastItem.getResumeDate()!=null && lastItem.getPauseDate() != null)
          else if (lastItem.resumeDate != null && lastItem.pauseDate != null) {
            // This was resumed/paused last time >> need to show Resume button
            return TimerPaused(
              taskId: taskId,
              startTime: task.taskCommencementTimeStamp!,
              elapsed: _calculateElapsedTime(task),
            );
          }
          // else if (lastItem.getPauseDate() == null)
          else if (lastItem.pauseDate == null) {
            // This was resumed but not paused last time >> need to show Pause button
            return TimerRunning(
              taskId: taskId,
              startTime: task.taskCommencementTimeStamp!,
              elapsed: _calculateElapsedTime(task),
            );
          }
        }
      }
    }

    // Default to stopped/initial state
    return TimerStopped(
      taskId: taskId,
      totalElapsed: Duration.zero,
    );
  }

  Duration _calculateElapsedTime(TaskDetailModel task) {
    if (task.taskCommencementTimeStamp == null) {
      return Duration.zero;
    }

    final startTime = task.taskCommencementTimeStamp!;
    final endTime = task.taskStoppedTimeStamp ?? DateTime.now();

    Duration totalElapsed = endTime.difference(startTime);

    // Subtract paused time
    Duration pausedTime = Duration.zero;

    for (final item in task.resumePauseItems) {
      if (item.pauseDate != null) {
        final pauseStart = item.pauseDate!;
        final pauseEnd =
            item.resumeDate ?? (task.taskStoppedTimeStamp ?? DateTime.now());

        pausedTime += pauseEnd.difference(pauseStart);
      }
    }

    return totalElapsed - pausedTime;
  }

  ResumePauseItemModel? _getLastResumePauseItem(
      List<ResumePauseItemModel> items) {
    if (items.isEmpty) return null;

    int maxId = 0;
    ResumePauseItemModel? lastItem;

    for (final item in items) {
      if (item.resumeOrderID != null) {
        final id = int.tryParse(item.resumeOrderID!) ?? 0;
        if (id > maxId) {
          maxId = id;
          lastItem = item;
        }
      }
    }

    return lastItem;
  }

  int _getMaxResumeOrderId(List<ResumePauseItemModel> items) {
    int maxId = 0;

    for (final item in items) {
      if (item.resumeOrderID != null) {
        final id = int.tryParse(item.resumeOrderID!) ?? 0;
        if (id > maxId) {
          maxId = id;
        }
      }
    }

    return maxId;
  }

  void _startPeriodicUpdates(int taskId) {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (state is TimerRunning) {
        loadTimerState(taskId);
      }
    });
  }

  TaskDetailModel? _getTaskById(int taskId) {
    final realm = _database.realm;
    return realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
  }

  void _addResumePauseItem(int taskId, String resumeOrderID,
      DateTime? resumeDate, DateTime? pauseDate) {
    final realm = _database.realm;
    final task =
        realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

    if (task != null) {
      realm.write(() {
        final pauseItem = ResumePauseItemModel(
          resumeOrderID: resumeOrderID,
          resumeDate: resumeDate,
          pauseDate: pauseDate,
        );
        task.resumePauseItems.add(pauseItem);
        task.isSynced = false;
      });
    }
  }

  void _updateResumePauseItemPauseDate(
      int taskId, String resumeOrderID, DateTime pauseDate) {
    final realm = _database.realm;
    final task =
        realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

    if (task != null) {
      realm.write(() {
        final item = task.resumePauseItems
            .where((item) => item.resumeOrderID == resumeOrderID)
            .firstOrNull;
        if (item != null) {
          item.pauseDate = pauseDate;
        }
        task.isSynced = false;
      });
    }
  }
}
